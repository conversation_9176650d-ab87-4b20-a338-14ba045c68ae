#!/usr/bin/env python3
"""
运行数据库迁移脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("开始运行数据库迁移...")
    
    try:
        # 运行 genre 和 theme 字段迁移
        from migrations.migrate_genre_theme_to_json import migrate_genre_theme_to_json
        migrate_genre_theme_to_json()
        print("✅ Genre 和 Theme 字段迁移完成")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return 1
    
    print("🎉 所有迁移完成！")
    return 0

if __name__ == "__main__":
    exit(main())
