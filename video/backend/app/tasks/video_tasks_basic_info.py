"""
视频处理相关的Celery任务
"""

from celery import current_task, chain, group
from app.tasks.celery import celery
from app.core.database import SessionLocal
from app.core.config import settings
from app.models.task import Video, AnalysisResult, Task
from app.services.task_service import TaskService
from app.services.audio_service import AudioService
from app.services.video_analysis_service import VideoAnalysisService

from app.services.ffmpeg_service import ffmpeg_service
from app.services.scene_detection_service import SceneDetectionService
from sqlalchemy.orm import Session
import os
import time
import json
from datetime import datetime
from loguru import logger

from .task_utils import safe_update_task_state
from .task_logger import TaskLogger


def _convert_to_srt_format(subtitle_content):
    """Convert subtitle content to SRT format"""
    srt_lines = []

    for entry in subtitle_content:
        index = entry.get('index', 1)
        start_time = _seconds_to_srt_time(entry.get('start_time', 0.0))
        end_time = _seconds_to_srt_time(entry.get('end_time', 0.0))
        text = entry.get('text', '')

        srt_lines.append(f"{index}")
        srt_lines.append(f"{start_time} --> {end_time}")
        srt_lines.append(text)
        srt_lines.append("")  # Empty line between entries

    return '\n'.join(srt_lines)


def _seconds_to_srt_time(seconds):
    """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)

    return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"


@celery.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def analyze_video_basic_info(self, video_id: int):
    """分析视频基础信息"""
    totalSteps = 10
    task_logger = TaskLogger("BASIC_INFO_ANALYSIS", video_id=video_id)
    task_logger.start_task(total_steps=10, description=f"视频 {video_id} 基础信息分析")

    db = SessionLocal()
    start_time = time.time()
    try:
        # 步骤1: 获取视频信息
        task_logger.start_step("获取视频信息")
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        task_logger.complete_step("获取视频信息", f"视频文件: {video.filename}, 大小: {video.file_size} bytes")

        # 步骤2: 检查依赖
        task_logger.start_step("检查系统依赖")
        deps = ffmpeg_service.check_dependencies()
        if not deps["ffprobe"]:
            raise Exception("ffprobe not available")
        task_logger.complete_step("检查系统依赖", f"ffprobe: {deps['ffprobe']}, ffmpeg: {deps.get('ffmpeg', False)}")

        # 更新任务状态
        task_service = TaskService(db)
        if video.task_id:
            task_service.set_status(video.task_id, "processing")

        # 步骤3: 分析视频元数据
        task_logger.start_step("分析视频元数据")
        safe_update_task_state(self, 'PROGRESS',
            {'current': 1, 'total': totalSteps, 'status': '分析视频元数据...'})

        if video.task_id:
            task_service.update_progress(video.task_id, 20)

        analysis_service = VideoAnalysisService(db)
        metadata_start = time.time()
        metadata = analysis_service.analyze_video_metadata(video_id)
        metadata_duration = time.time() - metadata_start
        
        task_logger.log_performance("视频元数据分析", metadata_duration, 
                                   f"分辨率: {metadata.get('resolution', 'N/A')}, 时长: {metadata.get('duration', 'N/A')}s")
        task_logger.complete_step("分析视频元数据", f"获取到 {len(metadata)} 个元数据字段")

        # 步骤4: 提取音频轨道
        task_logger.start_step("提取音频轨道")
        safe_update_task_state(self, 'PROGRESS',
            {'current': 2, 'total': totalSteps, 'status': '提取音频轨道...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 40)

        audio_service = AudioService(db)
        audio_start = time.time()
        extracted_audio_files = audio_service.extract_audio_tracks(video_id)
        audio_duration = time.time() - audio_start
        
        task_logger.log_performance("音频轨道提取", audio_duration, f"提取了 {len(extracted_audio_files)} 个音频文件")
        task_logger.complete_step("提取音频轨道", f"成功提取 {len(extracted_audio_files)} 个音频轨道")

        # 步骤5: 生成自动字幕
        task_logger.start_step("生成自动字幕")
        subtitle_info = None
        if not settings.SUBTITLE_AUTO_GENERATION_ENABLED:
            task_logger.complete_step("生成自动字幕", "字幕自动生成已禁用，跳过此步骤")
        elif extracted_audio_files:
            try:
                safe_update_task_state(self, 'PROGRESS',
                    {'current': 3, 'total': totalSteps, 'status': '生成自动字幕...'})

                # 使用简化的本地音频服务
                from app.services.local_audio_service import local_audio_service, AudioRequest
                from app.models.task import Subtitle
                from app.utils.file_organization import file_organizer

                subtitle_start = time.time()

                # 检查本地音频服务是否可用
                if not local_audio_service.is_available():
                    raise Exception("本地音频服务不可用")

                # 使用第一个音频文件进行处理
                audio_file_path = extracted_audio_files[0]
                if not os.path.exists(audio_file_path):
                    raise FileNotFoundError(f"音频文件不存在: {audio_file_path}")

                # 创建音频请求并处理
                audio_request = AudioRequest(input=audio_file_path)
                recognition_result = local_audio_service.process_audio(audio_request)

                # 转换识别结果为字幕格式
                subtitle_content = []
                if recognition_result and isinstance(recognition_result, list) and len(recognition_result) > 0:
                    # 处理返回的结果格式（基于示例JSON）
                    result_item = recognition_result[0]
                    if 'text' in result_item and 'timestamp' in result_item:
                        text = result_item['text']
                        timestamps = result_item['timestamp']

                        # 简单处理：将整个文本按时间戳分段
                        # 每个时间戳对应一个文本片段
                        if timestamps and len(timestamps) > 0:
                            # 按句号分割文本，但保持与时间戳数量的对应
                            sentences = text.split('。')
                            sentences = [s.strip() for s in sentences if s.strip()]

                            # 如果句子数量超过时间戳数量，合并句子
                            if len(sentences) > len(timestamps):
                                # 将多余的句子合并到最后一个时间段
                                combined_sentences = sentences[:len(timestamps)-1]
                                combined_sentences.append('。'.join(sentences[len(timestamps)-1:]))
                                sentences = combined_sentences

                            for i, (start_ms, end_ms) in enumerate(timestamps):
                                if i < len(sentences):
                                    sentence_text = sentences[i]
                                    if not sentence_text.endswith('。'):
                                        sentence_text += '。'

                                    subtitle_content.append({
                                        "index": i + 1,
                                        "start_time": start_ms / 1000.0,  # 转换为秒
                                        "end_time": end_ms / 1000.0,
                                        "text": sentence_text,
                                        "confidence": 0.9
                                    })

                # 计算平均置信度
                avg_confidence = sum(entry.get('confidence', 0.0) for entry in subtitle_content) / len(subtitle_content) if subtitle_content else 0.0

                # 生成SRT文件
                srt_file_path = file_organizer.get_subtitle_file_path(video_id, "zh-cn")
                srt_content = _convert_to_srt_format(subtitle_content)
                with open(srt_file_path, 'w', encoding='utf-8') as f:
                    f.write(srt_content)

                # 保存字幕记录到数据库
                subtitle = Subtitle(
                    video_id=video_id,
                    subtitle_type="auto_generated",
                    language="zh-cn",
                    file_path=str(srt_file_path),
                    content=json.dumps(subtitle_content),
                    confidence=avg_confidence,
                    processing_time=time.time() - subtitle_start
                )

                db.add(subtitle)
                db.commit()
                db.refresh(subtitle)

                subtitle_duration = time.time() - subtitle_start
                subtitle_info = {
                    "subtitle_id": subtitle.id,
                    "confidence": avg_confidence,
                    "segments_count": len(subtitle_content),
                    "srt_file_path": str(srt_file_path)
                }

                task_logger.log_performance("自动字幕生成", subtitle_duration, f"字幕ID: {subtitle.id}")
                task_logger.complete_step("生成自动字幕", f"成功生成字幕，置信度: {avg_confidence:.2f}")
            except Exception as e:
                task_logger.log_warning("自动字幕生成失败", e)
                task_logger.complete_step("生成自动字幕", "字幕生成失败，跳过此步骤")
        else:
            task_logger.complete_step("生成自动字幕", "无音频轨道，跳过字幕生成")

        # 步骤6: 提取视频帧
        task_logger.start_step("提取视频帧")
        safe_update_task_state(self, 'PROGRESS',
            {'current': 4, 'total': totalSteps, 'status': '提取视频帧...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 60)

        frame_start = time.time()
        frame_files = analysis_service.extract_video_frames(video_id)
        frame_duration = time.time() - frame_start

        task_logger.log_performance("视频帧提取", frame_duration, f"提取了 {len(frame_files)} 个帧")
        task_logger.complete_step("提取视频帧", f"成功提取 {len(frame_files)} 个帧")

        # 步骤7: 镜头检测和分割
        task_logger.start_step("检测镜头切换并分割视频")
        safe_update_task_state(self, 'PROGRESS',
            {'current': 5, 'total': totalSteps, 'status': '检测镜头切换并分割视频...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 80)

        scene_service = SceneDetectionService(db)
        scene_start = time.time()

        # 首先检测场景
        scenes = scene_service.detect_scenes(
            video_id=video_id,
            detector_type="content",
            threshold=settings.SCENE_DETECTION_THRESHOLD,
            min_scene_len=settings.SCENE_MIN_LENGTH
        )

        # 然后自动分割视频（如果启用）
        scene_clips = []
        if scenes and settings.SCENE_AUTO_SPLIT_ENABLED:
            try:
                scene_clips = scene_service.split_video_by_scenes(video_id, settings.SCENE_SPLIT_FORMAT)
                task_logger.log_info(f"成功分割视频为 {len(scene_clips)} 个场景片段")
            except Exception as e:
                task_logger.log_warning(f"场景分割失败: {e}")
                # 分割失败不影响整体分析流程

        scene_duration = time.time() - scene_start
        avg_scene_duration = sum(s["duration"] for s in scenes) / len(scenes) if scenes else 0
        task_logger.log_performance("镜头检测和分割", scene_duration,
                                   f"检测到 {len(scenes)} 个镜头, 分割了 {len(scene_clips)} 个片段, 平均时长: {avg_scene_duration:.2f}s")
        task_logger.complete_step("检测镜头切换并分割视频", f"检测到 {len(scenes)} 个镜头，分割了 {len(scene_clips)} 个片段")

        # 步骤8: 比特率分析
        task_logger.start_step("分析比特率统计")
        safe_update_task_state(self, 'PROGRESS',
            {'current': 6, 'total': totalSteps, 'status': '分析比特率统计...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 85)

        bitrate_start = time.time()
        bitrate_stats = analysis_service.analyze_video_bitrate_stats(
            video_id=video_id,
            stream_type="video",
            aggregation="time",
            chunk_size=30.0
        )
        bitrate_duration = time.time() - bitrate_start
        
        task_logger.log_performance("比特率分析", bitrate_duration, 
                                   f"平均比特率: {bitrate_stats.avg_bitrate:.0f} bps, 帧数: {bitrate_stats.num_frames}")
        task_logger.complete_step("分析比特率统计", f"分析完成，平均比特率: {bitrate_stats.avg_bitrate:.0f} bps")

        # 步骤9: 生成综合信息
        task_logger.start_step("生成综合信息")
        safe_update_task_state(self, 'PROGRESS',
            {'current': 7, 'total': totalSteps, 'status': '生成综合信息...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 90)

        json_start = time.time()
        comprehensive_json_path = analysis_service.generate_comprehensive_video_info(video_id)
        json_duration = time.time() - json_start
        
        # 获取生成的JSON文件大小
        json_size = os.path.getsize(comprehensive_json_path) if os.path.exists(comprehensive_json_path) else 0
        task_logger.log_performance("综合信息生成", json_duration, 
                                   f"JSON文件大小: {json_size / 1024 / 1024:.2f} MB")
        task_logger.complete_step("生成综合信息", f"JSON文件已保存: {os.path.basename(comprehensive_json_path)}")

        # 计算质量评估（基于实际数据）
        quality_data = {
            "clarity": min(90, max(60, int(80 + (bitrate_stats.avg_bitrate / 1000 - 2000) / 100))),
            "stability": min(90, max(70, int(85 - len(scenes) / 10))),
            "saturation": min(90, max(65, int(75 + (video.fps - 24) * 2)))
        }

        # 获取音频编码信息
        audio_codec = "AAC"
        if extracted_audio_files and len(extracted_audio_files) > 0:
            audio_codec = metadata.get("audio_codec", "AAC")

        # 保存分析结果
        result_data = {
            "metadata": metadata,
            "audio_tracks_extracted": len(extracted_audio_files),
            "frames_extracted": len(frame_files),
            "audio_files": extracted_audio_files,
            "frame_files": frame_files[:10],
            "subtitle_info": subtitle_info,
            "scene_detection": {
                "total_scenes": len(scenes),
                "scenes": scenes[:10],
                "avg_scene_duration": avg_scene_duration,
                "scene_clips_generated": len(scene_clips),
                "scene_clips": scene_clips[:10] if scene_clips else []
            },
            "bitrate_stats": {
                "avg_fps": bitrate_stats.avg_fps,
                "num_frames": bitrate_stats.num_frames,
                "avg_bitrate": bitrate_stats.avg_bitrate,
                "max_bitrate": bitrate_stats.max_bitrate,
                "min_bitrate": bitrate_stats.min_bitrate,
                "duration": bitrate_stats.duration,
                "chunks": len(bitrate_stats.bitrate_per_chunk) if bitrate_stats.bitrate_per_chunk else 0
            },
            "comprehensive_json_path": comprehensive_json_path,
            "quality": quality_data,
            "audio_codec": audio_codec
        }

        # 步骤10: 保存分析结果
        task_logger.start_step("保存分析结果")
        end_time = time.time()
        processing_duration = end_time - start_time

        analysis_result = AnalysisResult(
            video_id=video_id,
            step="basic_analysis",
            result=result_data,
            confidence=0.95,
            processing_time=processing_duration,
            processing_duration_seconds=processing_duration,
            started_at=datetime.fromtimestamp(start_time),
            completed_at=datetime.fromtimestamp(end_time)
        )

        db.add(analysis_result)

        # 更新视频状态
        video.status = "analyzed"

        # 完成任务
        safe_update_task_state(self, 'PROGRESS',
            {'current': 8, 'total': totalSteps, 'status': '分析完成'})
        if video.task_id:
            task_service.update_progress(video.task_id, 100)
            task_service.set_status(video.task_id, "completed")

        db.commit()
        
        task_logger.complete_step("保存分析结果", f"结果已保存到数据库，置信度: 0.95")
        task_logger.complete_task(True, f"基础信息分析完成，总耗时: {processing_duration:.2f}s")

        return {
            'status': 'completed',
            'result': result_data,
            'video_id': video_id
        }

    except Exception as e:
        # 错误处理
        error_message = str(e)
        task_logger.log_error("基础信息分析失败", e)

        # 更新视频状态为失败
        video.status = "failed"

        # 更新任务状态
        if video.task_id:
            task_service = TaskService(db)
            task_service.set_status(video.task_id, "failed")
            task_service.update_task(video.task_id, error_message=error_message)

        db.commit()

        # 更新Celery任务状态
        safe_update_task_state(self, 'FAILURE', {
            'error': error_message,
            'video_id': video_id,
            'retry_count': getattr(self.request, 'retries', 0) if hasattr(self, 'request') else 0,
            'max_retries': getattr(self, 'max_retries', 3)
        })
        
        task_logger.complete_task(False, f"基础信息分析失败: {error_message}")
        raise e
    finally:
        db.close()