#!/usr/bin/env python3
"""
测试增量视频处理功能

这个脚本用于验证当任务中添加新视频时，系统只处理新增的视频，
而保留之前已经处理过的结果。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.task import Task, Video
from app.services.task_service import TaskService
from sqlalchemy.orm import Session


def create_test_scenario(db: Session):
    """创建测试场景"""
    print("=== 创建测试场景 ===")
    
    # 创建测试任务
    task_service = TaskService(db)
    task = task_service.create_task(
        name="增量处理测试任务",
        description="测试增量视频处理功能"
    )
    print(f"创建测试任务: {task.id} - {task.name}")
    
    # 创建3个已处理的视频
    processed_videos = []
    for i in range(1, 4):
        video = Video(
            task_id=task.id,
            filename=f"processed_video_{i}.mp4",
            original_filename=f"processed_video_{i}.mp4",
            file_path=f"/fake/path/processed_video_{i}.mp4",
            file_size=1024 * 1024 * 10,  # 10MB
            status="analyzed"  # 已处理状态
        )
        db.add(video)
        processed_videos.append(video)
    
    # 创建2个新上传的视频
    new_videos = []
    for i in range(1, 3):
        video = Video(
            task_id=task.id,
            filename=f"new_video_{i}.mp4",
            original_filename=f"new_video_{i}.mp4",
            file_path=f"/fake/path/new_video_{i}.mp4",
            file_size=1024 * 1024 * 15,  # 15MB
            status="uploaded"  # 新上传状态
        )
        db.add(video)
        new_videos.append(video)
    
    # 创建1个处理失败的视频
    failed_video = Video(
        task_id=task.id,
        filename="failed_video.mp4",
        original_filename="failed_video.mp4",
        file_path="/fake/path/failed_video.mp4",
        file_size=1024 * 1024 * 8,  # 8MB
        status="failed"  # 失败状态
    )
    db.add(failed_video)
    
    db.commit()
    
    print(f"创建了 {len(processed_videos)} 个已处理视频")
    print(f"创建了 {len(new_videos)} 个新上传视频")
    print(f"创建了 1 个处理失败视频")
    
    return task.id


def test_incremental_logic(db: Session, task_id: int):
    """测试增量处理逻辑"""
    print(f"\n=== 测试任务 {task_id} 的增量处理逻辑 ===")
    
    # 获取所有视频
    all_videos = db.query(Video).filter(Video.task_id == task_id).all()
    print(f"任务总视频数: {len(all_videos)}")
    
    # 统计各状态的视频数量
    analyzed_videos = [v for v in all_videos if v.status == "analyzed"]
    pending_videos = [v for v in all_videos if v.status in ["uploaded", "failed"]]
    
    total_videos = len(all_videos)
    analyzed_count = len(analyzed_videos)
    pending_count = len(pending_videos)
    
    print(f"已处理视频: {analyzed_count} 个")
    print(f"待处理视频: {pending_count} 个")
    
    # 模拟增量处理逻辑
    if pending_count == 0:
        print("✅ 所有视频都已处理完成，无需重复处理")
        return {
            'status': 'completed',
            'task_id': task_id,
            'total_videos': total_videos,
            'analyzed_videos': analyzed_count,
            'pending_videos': 0,
            'message': '所有视频都已处理完成'
        }
    else:
        print(f"🔄 需要处理 {pending_count} 个新增/失败的视频")
        print("待处理视频列表:")
        for video in pending_videos:
            print(f"  - {video.filename} (状态: {video.status})")
        
        # 计算当前进度
        current_progress = (analyzed_count / total_videos) * 100
        print(f"当前进度: {current_progress:.1f}% ({analyzed_count}/{total_videos})")
        
        return {
            'status': 'processing',
            'task_id': task_id,
            'total_videos': total_videos,
            'analyzed_videos': analyzed_count,
            'pending_videos': pending_count,
            'current_progress': current_progress,
            'message': f'需要处理 {pending_count} 个新增/失败的视频'
        }


def test_add_more_videos(db: Session, task_id: int):
    """测试添加更多视频后的情况"""
    print(f"\n=== 模拟添加更多视频到任务 {task_id} ===")
    
    # 添加2个新视频
    for i in range(3, 5):
        video = Video(
            task_id=task_id,
            filename=f"additional_video_{i}.mp4",
            original_filename=f"additional_video_{i}.mp4",
            file_path=f"/fake/path/additional_video_{i}.mp4",
            file_size=1024 * 1024 * 12,  # 12MB
            status="uploaded"  # 新上传状态
        )
        db.add(video)
    
    db.commit()
    print("添加了 2 个新视频")
    
    # 重新测试增量处理逻辑
    return test_incremental_logic(db, task_id)


def cleanup_test_data(db: Session, task_id: int):
    """清理测试数据"""
    print(f"\n=== 清理测试数据 ===")
    
    # 删除测试视频
    videos = db.query(Video).filter(Video.task_id == task_id).all()
    for video in videos:
        db.delete(video)
    
    # 删除测试任务
    task = db.query(Task).filter(Task.id == task_id).first()
    if task:
        db.delete(task)
    
    db.commit()
    print(f"已清理任务 {task_id} 的所有测试数据")


def main():
    """主测试函数"""
    print("🚀 开始测试增量视频处理功能")
    
    db = SessionLocal()
    try:
        # 1. 创建测试场景
        task_id = create_test_scenario(db)
        
        # 2. 测试初始增量处理逻辑
        result1 = test_incremental_logic(db, task_id)
        print(f"测试结果1: {result1}")
        
        # 3. 测试添加更多视频后的情况
        result2 = test_add_more_videos(db, task_id)
        print(f"测试结果2: {result2}")
        
        # 4. 模拟所有视频都处理完成的情况
        print(f"\n=== 模拟所有视频处理完成 ===")
        all_videos = db.query(Video).filter(Video.task_id == task_id).all()
        for video in all_videos:
            video.status = "analyzed"
        db.commit()
        
        result3 = test_incremental_logic(db, task_id)
        print(f"测试结果3: {result3}")
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理测试数据
        if 'task_id' in locals():
            cleanup_test_data(db, task_id)
        db.close()


if __name__ == "__main__":
    main()
