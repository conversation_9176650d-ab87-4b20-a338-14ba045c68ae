# 增量视频处理功能实现

## 功能概述

实现了增量视频处理功能，当任务中后来添加了新视频时，系统只对新增的视频进行处理，保留此前的结果并跳过已处理的视频。

## 核心改进

### 1. 修改了 `process_task_videos` 函数

**文件**: `app/tasks/video_tasks.py`

**主要变更**:
- 将原来获取所有视频的逻辑改为区分已处理和待处理视频
- 只对状态为 `uploaded` 或 `failed` 的视频进行处理
- 跳过状态为 `analyzed` 的已处理视频

**核心逻辑**:
```python
# 统计各状态的视频数量
analyzed_videos = [v for v in all_videos if v.status == "analyzed"]
pending_videos = [v for v in all_videos if v.status in ["uploaded", "failed"]]

# 如果没有待处理视频，直接标记任务完成
if pending_count == 0:
    task_service.set_status(task_id, "completed")
    task_service.update_progress(task_id, 100.0)
    return {'status': 'completed', 'message': '所有视频都已处理完成'}

# 只处理待处理的视频
for video in pending_videos:
    # 创建分析任务链...
```

### 2. 修改了 `process_task_videos_with_minicpm` 函数

**文件**: `app/tasks/video_tasks_minicpm.py`

**变更内容**: 应用了与标准处理函数相同的增量处理逻辑

## 视频状态管理

### 视频状态定义
- `uploaded`: 新上传的视频，需要处理
- `analyzing`: 正在分析中
- `analyzed`: 已完成分析，跳过处理
- `failed`: 处理失败，需要重新处理

### 增量处理逻辑
1. **获取统计信息**: 统计任务中各状态的视频数量
2. **检查处理需求**: 如果没有待处理视频，直接完成任务
3. **计算进度**: 基于总视频数计算当前进度
4. **选择性处理**: 只对新增/失败的视频创建处理任务

## 用户体验改进

### 1. 智能任务状态管理
- 添加新视频时，任务状态自动重置为 `pending`
- 启动处理时，如果所有视频已处理完成，任务直接标记为 `completed`

### 2. 准确的进度计算
- 进度基于总视频数计算，而不是待处理视频数
- 显示已完成和总数的比例：`已完成 3/5 个视频`

### 3. 详细的日志信息
```
总计 5 个视频：已处理 3 个，待处理 2 个
需要处理 2 个新增/失败的视频
开始处理 2 个新增视频（已完成 3/5）...
```

## 使用场景示例

### 场景1: 初始处理
1. 用户创建任务，上传3个视频
2. 启动处理，系统处理所有3个视频
3. 任务完成，状态为 `completed`

### 场景2: 增量处理
1. 用户向已完成的任务添加2个新视频
2. 任务状态自动变为 `pending`
3. 启动处理，系统只处理新增的2个视频
4. 保留之前3个视频的处理结果
5. 最终任务包含5个已处理视频

### 场景3: 失败重试
1. 某个视频处理失败，状态为 `failed`
2. 用户修复问题后重新启动任务
3. 系统只重新处理失败的视频
4. 其他已成功处理的视频保持不变

## 技术实现细节

### 1. 数据库查询优化
```python
# 一次查询获取所有视频
all_videos = db.query(Video).filter(Video.task_id == task_id).all()

# 在内存中分类，避免多次数据库查询
analyzed_videos = [v for v in all_videos if v.status == "analyzed"]
pending_videos = [v for v in all_videos if v.status in ["uploaded", "failed"]]
```

### 2. 任务链创建优化
```python
# 只为待处理视频创建任务链
for video in pending_videos:
    video_chain = chain(
        analyze_video_basic_info.s(video.id),
        analyze_video_content.si(video.id),
        analyze_video_plot.si(video.id)
    )
    video_tasks.append(video_chain)
```

### 3. 进度监控改进
```python
# 从已完成数量开始计算
completed_count = analyzed_count

# 监控时包括之前已完成的视频
current_completed_videos = db.query(Video).filter(
    Video.task_id == task_id,
    Video.status == "analyzed"
).count()
```

## 兼容性说明

- 现有API接口保持不变
- 前端无需修改，自动享受增量处理功能
- 向后兼容，不影响现有任务的处理

## 测试验证

创建了测试脚本 `test_incremental_processing.py` 来验证功能：
- 模拟已处理视频和新增视频的场景
- 验证增量处理逻辑的正确性
- 测试各种边界情况

## 总结

通过实现增量视频处理功能，系统现在能够：
1. ✅ 智能识别新增视频和已处理视频
2. ✅ 只处理需要处理的视频，节省计算资源
3. ✅ 保留之前的处理结果，避免重复工作
4. ✅ 提供准确的进度反馈和日志信息
5. ✅ 支持失败重试，只重新处理失败的视频

这大大提高了系统的效率和用户体验，特别是在处理大量视频或需要分批添加视频的场景中。
